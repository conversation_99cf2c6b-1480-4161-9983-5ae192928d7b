backend-1 | 172.20.0.1:65480 - "OPTIONS /api/composio-mcp/discover-tools HTTP/1.1" 200
backend-1 | 🔍 CONNECTING TO MCP SERVER: https://mcp.composio.dev/partner/composio/gmail/mcp?customerId=e88bb454-b5a4-5939-89e2-66f557dc06a2
backend-1 | 🔍 MCP SESSION INITIALIZED
backend-1 | 🔍 MCP RESPONSE DEBUG:
backend-1 | Type: <class 'mcp.types.ListToolsResult'>
backend-1 | Attributes: ['__abstractmethods__', '__annotations__', '__class__', '__class_getitem__', '__class_vars__', '__copy__', '__deepcopy__', '__delattr__', '__dict__', '__dir__', '__doc__', '__eq__', '__fields__', '__fields_set__', '__format__', '__ge__', '__get_pydantic_core_schema__', '__get_pydantic_json_schema__', '__getattr__', '__getattribute__', '__getstate__', '__gt__', '__hash__', '__init__', '__init_subclass__', '__iter__', '__le__', '__lt__', '__module__', '__ne__', '__new__', '__pretty__', '__private_attributes__', '__pydantic_complete__', '__pydantic_computed_fields__', '__pydantic_core_schema__', '__pydantic_custom_init__', '__pydantic_decorators__', '__pydantic_extra__', '__pydantic_fields__', '__pydantic_fields_set__', '__pydantic_generic_metadata__', '__pydantic_init_subclass__', '__pydantic_parent_namespace__', '__pydantic_post_init__', '__pydantic_private__', '__pydantic_root_model__', '__pydantic_serializer__', '__pydantic_setattr_handlers__', '__pydantic_validator__', '__reduce__', '__reduce_ex__', '__replace__', '__repr__', '__repr_args__', '__repr_name__', '__repr_recursion__', '__repr_str__', '__rich_repr__', '__setattr__', '__setstate__', '__signature__', '__sizeof__', '__slots__', '__str__', '__subclasshook__', '__weakref__', '_abc_impl', '_calculate_keys', '_copy_and_set_values', '_get_value', '_iter', '_setattr_handler', 'construct', 'copy', 'dict', 'from_orm', 'json', 'meta', 'model_computed_fields', 'model_config', 'model_construct', 'model_copy', 'model_dump', 'model_dump_json', 'model_extra', 'model_fields', 'model_fields_set', 'model_json_schema', 'model_parametrized_name', 'model_post_init', 'model_rebuild', 'model_validate', 'model_validate_json', 'model_validate_strings', 'nextCursor', 'parse_file', 'parse_obj', 'parse_raw', 'schema', 'schema_json', 'tools', 'update_forward_refs', 'validate']
backend-1 | Attribute values:
backend-1 | meta: None
backend-1 | model_computed_fields: {}
backend-1 | model_config: {'extra': 'allow'}
backend-1 | model_extra: {}
backend-1 | model_fields: {'meta': FieldInfo(annotation=Union[dict[str, Any], NoneType], required=False, default=None, alias='\_meta', alias_priority=2), 'nextCursor': FieldInfo(annotation=Union[str, NoneType], required=False, default=None), 'tools': FieldInfo(annotation=list[Tool], required=True)}
backend-1 | model_fields_set: {'tools'}
backend-1 | nextCursor: None
backend-1 | tools: [Tool(name='GMAIL_DELETE_DRAFT', description='Permanently deletes a specific gmail draft using its id; ensure the draft exists and the user has necessary permissions for the given `user id`.', inputSchema={'type': 'object', 'properties': {'draft_id': {'type': 'string', 'description': 'Immutable ID of the draft to delete, typically obtained when the draft was created.'}, 'user_id': {'type': 'string', 'description': "User's email address or 'me' for the authenticated user; 'me' is recommended.", 'default': 'me'}}, 'required': ['draft_id'], 'additionalProperties': False, '$schema': 'http://json-schema.org/draft-07/schema#'}, annotations=ToolAnnotations(title='GMAIL_DELETE_DRAFT', readOnlyHint=False, destructiveHint=True, idempotentHint=True, openWorldHint=True, scopes=[])), Tool(name='GMAIL_DELETE_MESSAGE', description="Permanently deletes a specific email message by its id from a gmail mailbox; for `user id`, use 'me' for the authenticated user or an email address to which the authenticated user has delegated access.", inputSchema={'type': 'object', 'properties': {'message_id': {'type': 'string', 'description': 'Identifier of the email message to delete.'}, 'user_id': {'type': 'string', 'description': "User's email address. The special value 'me' refers to the authenticated user.", 'default': 'me'}}, 'required': ['message_id'], 'additionalProperties': False, '$schema': 'http://json-schema.org/draft-07/schema#'}, annotations=ToolAnnotations(title='GMAIL_DELETE_MESSAGE', readOnlyHint=False, destructiveHint=True, idempotentHint=False, openWorldHint=True, scopes=[])), Tool(name='GMAIL_FETCH_EMAILS', description='Fetches a list of email messages from a gmail account, supporting filtering, pagination, and optional full content retrieval.', inputSchema={'type': 'object', 'properties': {'include_payload': {'type': 'boolean', 'description': 'Set to true to include full message payload (headers, body, attachments); false for metadata only.', 'default': True}, 'include_spam_trash': {'type': 'boolean', 'description': "Set to true to include messages from 'SPAM' and 'TRASH'.", 'default': False}, 'label_ids': {'type': 'array', 'items': {'type': 'string'}, 'description': "Filter by label IDs; only messages with all specified labels are returned. Common IDs: 'INBOX', 'SPAM', 'TRASH', 'UNREAD', 'STARRED', 'IMPORTANT', 'CATEGORY_PERSONAL', 'CATEGORY_SOCIAL', 'CATEGORY_PROMOTIONS', 'CATEGORY_UPDATES', 'CATEGORY_FORUMS'. Use 'listLabels' action for custom IDs."}, 'max_results': {'type': 'integer', 'minimum': 1, 'maximum': 500, 'description': 'Maximum number of messages to retrieve per page.', 'default': 1}, 'page_token': {'type': ['string', 'null'], 'description': "Token for retrieving a specific page, obtained from a previous response's `nextPageToken`. Omit for the first page.", 'default': None}, 'query': {'type': ['string', 'null'], 'description': "Gmail advanced search query (e.g., 'from:user subject:meeting'). Supports operators like 'from:', 'to:', 'subject:', 'label:', 'has:attachment', 'is:unread', 'after:YYYY/MM/DD', 'before:YYYY/MM/DD', AND/OR/NOT. Use quotes for exact phrases. Omit for no query filter.", 'default': None}, 'user_id': {'type': 'string', 'description': "User's email address or 'me' for the authenticated user.", 'default': 'me'}}, 'additionalProperties': False, '$schema': 'http://json-schema.org/draft-07/schema#'}, annotations=ToolAnnotations(title='GMAIL_FETCH_EMAILS', readOnlyHint=True, destructiveHint=False, idempotentHint=False, openWorldHint=True, scopes=[])), Tool(name='GMAIL_GET_CONTACTS', description='Fetches contacts (connections) for the authenticated google account, allowing selection of specific data fields and pagination.', inputSchema={'type': 'object', 'properties': {'page_token': {'type': 'string', 'description': "Token to retrieve a specific page of results, obtained from 'nextPageToken' in a previous response."}, 'person_fields': {'type': 'string', 'description': "Comma-separated person fields to retrieve for each contact (e.g., 'names,emailAddresses').", 'default': 'emailAddresses,names,birthdays,genders'}, 'resource_name': {'type': 'string', 'description': "Identifier for the person resource whose connections are listed; use 'people/me' for the authenticated user.", 'default': 'people/me'}}, 'additionalProperties': False, '$schema': 'http://json-schema.org/draft-07/schema#'}, annotations=ToolAnnotations(title='GMAIL_GET_CONTACTS', readOnlyHint=True, destructiveHint=False, idempotentHint=False, openWorldHint=True, scopes=[])), Tool(name='GMAIL_LIST_DRAFTS', description="Retrieves a paginated list of email drafts from a user's gmail account.", inputSchema={'type': 'object', 'properties': {'max_results': {'type': 'integer', 'minimum': 1, 'maximum': 500, 'description': 'Maximum number of drafts to return per page.', 'default': 1}, 'page_token': {'type': 'string', 'description': 'Token from a previous response to retrieve a specific page of drafts.', 'default': ''}, 'user_id': {'type': 'string', 'description': "User's mailbox ID; use 'me' for the authenticated user.", 'default': 'me'}}, 'additionalProperties': False, '$schema': 'http://json-schema.org/draft-07/schema#'}, annotations=ToolAnnotations(title='GMAIL_LIST_DRAFTS', readOnlyHint=True, destructiveHint=False, idempotentHint=False, openWorldHint=True, scopes=[])), Tool(name='GMAIL_MOVE_TO_TRASH', description='Moves an existing, non-deleted email message to the trash for the specified user.', inputSchema={'type': 'object', 'properties': {'message_id': {'type': 'string', 'description': 'Identifier of the email message to move to trash.'}, 'user_id': {'type': 'string', 'description': "User's email address or 'me' for the authenticated user.", 'default': 'me'}}, 'required': ['message_id'], 'additionalProperties': False, '$schema': 'http://json-schema.org/draft-07/schema#'}, annotations=ToolAnnotations(title='GMAIL_MOVE_TO_TRASH', readOnlyHint=False, destructiveHint=True, idempotentHint=False, openWorldHint=True, scopes=[])), Tool(name='GMAIL_PATCH_LABEL', description='Patches the specified label.', inputSchema={'type': 'object', 'properties': {'color': {'description': 'The color to assign to the label. Color is only available for labels that have their `type` set to `user`.', 'default': None, 'type': 'object', 'properties': {'backgroundColor': {'type': ['string', 'null'], 'description': 'The background color of the label, represented as a hex string. Example: #ffffff', 'default': None}, 'textColor': {'type': ['string', 'null'], 'description': 'The text color of the label, represented as a hex string. Example: #000000', 'default': None}}, 'additionalProperties': False, 'nullable': True}, 'id': {'type': 'string', 'description': 'The ID of the label to update.'}, 'labelListVisibility': {'type': ['string', 'null'], 'description': 'The visibility of the label in the label list in the Gmail web interface.', 'default': None}, 'messageListVisibility': {'type': ['string', 'null'], 'description': 'The visibility of messages with this label in the message list in the Gmail web interface.', 'default': None}, 'name': {'type': ['string', 'null'], 'description': 'The display name of the label.', 'default': None}, 'userId': {'type': 'string', 'description': "The user's email address. The special value `me` can be used to indicate the authenticated user."}}, 'required': ['id', 'userId'], 'additionalProperties': False, '$schema': 'http://json-schema.org/draft-07/schema#'}, annotations=ToolAnnotations(title='GMAIL_PATCH_LABEL', readOnlyHint=False, destructiveHint=False, idempotentHint=True, openWorldHint=True, scopes=[])), Tool(name='GMAIL_REPLY_TO_THREAD', description="Sends a reply within a specific gmail thread using the original thread's subject, requiring a valid `thread id` and correctly formatted email addresses.", inputSchema={'type': 'object', 'properties': {'bcc': {'type': 'array', 'items': {'type': 'string'}, 'description': "BCC recipients' email addresses (hidden from other recipients).", 'default': []}, 'cc': {'type': 'array', 'items': {'type': 'string'}, 'description': "CC recipients' email addresses.", 'default': []}, 'extra_recipients': {'type': 'array', 'items': {'type': 'string'}, 'description': "Additional 'To' recipients' email addresses.", 'default': []}, 'is_html': {'type': 'boolean', 'description': 'Indicates if `message_body` is HTML; if True, body must be valid HTML, if False, body should not contain HTML tags.', 'default': False}, 'message_body': {'type': 'string', 'description': 'Content of the reply message, either plain text or HTML.'}, 'recipient_email': {'type': 'string', 'description': "Primary recipient's email address."}, 'thread_id': {'type': 'string', 'description': 'Identifier of the Gmail thread for the reply.'}, 'user_id': {'type': 'string', 'description': "Identifier for the user sending the reply; 'me' refers to the authenticated user.", 'default': 'me'}}, 'required': ['message_body', 'recipient_email', 'thread_id'], 'additionalProperties': False, '$schema': 'http://json-schema.org/draft-07/schema#'}, annotations=ToolAnnotations(title='GMAIL_REPLY_TO_THREAD', readOnlyHint=False, destructiveHint=False, idempotentHint=False, openWorldHint=True, scopes=[])), Tool(name='GMAIL_SEARCH_PEOPLE', description="Searches contacts by matching the query against names, nicknames, emails, phone numbers, and organizations, optionally including 'other contacts'.", inputSchema={'type': 'object', 'properties': {'other_contacts': {'type': 'boolean', 'description': "Include 'Other Contacts' (interacted with but not explicitly saved) in search results; if false, searches only primary contacts.", 'default': False}, 'pageSize': {'type': 'integer', 'minimum': 0, 'maximum': 30, 'description': 'Maximum results to return; values >30 are capped to 30 by the API.', 'default': 10}, 'person_fields': {'type': 'string', 'description': "Comma-separated fields to return (e.g., 'names,emailAddresses'); see PersonFields enum. If 'other_contacts' is true, only 'emailAddresses', 'names', 'phoneNumbers' are allowed.", 'default': 'emailAddresses,names,phoneNumbers'}, 'query': {'type': 'string', 'description': 'Matches contact names, nicknames, email addresses, phone numbers, and organization fields.'}}, 'required': ['query'], 'additionalProperties': False, '$schema': 'http://json-schema.org/draft-07/schema#'}, annotations=ToolAnnotations(title='GMAIL_SEARCH_PEOPLE', readOnlyHint=True, destructiveHint=False, idempotentHint=False, openWorldHint=True, scopes=[])), Tool(name='GMAIL_SEND_DRAFT', description='Sends the specified, existing draft to the recipients in the to, cc, and bcc headers.', inputSchema={'type': 'object', 'properties': {'draft_id': {'type': 'string', 'description': 'The ID of the draft to send.'}, 'user_id': {'type': 'string', 'description': "The user's email address. The special value `me` can be used to indicate the authenticated user.", 'default': 'me'}}, 'required': ['draft_id'], 'additionalProperties': False, '$schema': 'http://json-schema.org/draft-07/schema#'}, annotations=ToolAnnotations(title='GMAIL_SEND_DRAFT', readOnlyHint=False, destructiveHint=False, idempotentHint=False, openWorldHint=True, scopes=[])), Tool(name='GMAIL_CHECK_ACTIVE_CONNECTION', description='Check if any active connections exist for the gmail app or verify the status of a connection with a specific ID. If an active connection exists, returns true, otherwise returns false. Active connections allow an agent to perform actions with the gmail app.', inputSchema={'type': 'object', 'properties': {'connection_id': {'type': 'string', 'description': 'ID of the connection to check', 'default': None}, 'tool': {'type': 'string', 'description': 'Name of the tool, for example: GitHub', 'default': None}}, 'required': ['connection_id', 'tool'], 'additionalProperties': False, '$schema': 'http://json-schema.org/draft-07/schema#'}, annotations=ToolAnnotations(title='GMAIL_CHECK_ACTIVE_CONNECTION', readOnlyHint=None, destructiveHint=None, idempotentHint=None, openWorldHint=None, scopes=[])), Tool(name='GMAIL_INITIATE_CONNECTION', description="Initiate a connection to the gmail app. For example, gmail might require specific parameters like 'api_key' to create a connection, which we can get by calling GMAIL_GET_REQUIRED_PARAMETERS. To create a new connection to gmail, call this tool with the required parameters.", inputSchema={'type': 'object', 'properties': {'parameters': {'description': "Optional parameters required for connection. Not needed if using a default connector. Examples: {'api_key': '1234567890'}", 'default': None, 'type': 'object', 'additionalProperties': {}, 'nullable': True}, 'tool': {'type': 'string', 'description': 'Name of the tool like GitHub'}}, 'required': ['tool'], 'additionalProperties': False, '$schema': 'http://json-schema.org/draft-07/schema#'}, annotations=ToolAnnotations(title='GMAIL_INITIATE_CONNECTION', readOnlyHint=None, destructiveHint=None, idempotentHint=None, openWorldHint=None, scopes=[])), Tool(name='GMAIL_GET_REQUIRED_PARAMETERS', description="Retrieves the necessary parameters required to initiate a connection with gmail. Different tools require different parameters based on their authentication scheme. For example, gmail might require an API key, a subdomain, or a webhook URL. Tools using OAuth2 typically don't require parameters as they redirect users to the tool's website for authorization.", inputSchema={'type': 'object', 'properties': {'tool': {'type': 'string', 'description': 'Name of the tool For example: github'}}, 'required': ['tool'], 'additionalProperties': False, '$schema': 'http://json-schema.org/draft-07/schema#'}, annotations=ToolAnnotations(title='GMAIL_GET_REQUIRED_PARAMETERS', readOnlyHint=None, destructiveHint=None, idempotentHint=None, openWorldHint=None, scopes=[]))]
backend-1  |   Response __dict__: {'meta': None, 'nextCursor': None, 'tools': [Tool(name='GMAIL_DELETE_DRAFT', description='Permanently deletes a specific gmail draft using its id; ensure the draft exists and the user has necessary permissions for the given `user id`.', inputSchema={'type': 'object', 'properties': {'draft_id': {'type': 'string', 'description': 'Immutable ID of the draft to delete, typically obtained when the draft was created.'}, 'user_id': {'type': 'string', 'description': "User's email address or 'me' for the authenticated user; 'me' is recommended.", 'default': 'me'}}, 'required': ['draft_id'], 'additionalProperties': False, '$schema': 'http://json-schema.org/draft-07/schema#'}, annotations=ToolAnnotations(title='GMAIL_DELETE_DRAFT', readOnlyHint=False, destructiveHint=True, idempotentHint=True, openWorldHint=True, scopes=[])), Tool(name='GMAIL_DELETE_MESSAGE', description="Permanently deletes a specific email message by its id from a gmail mailbox; for `user id`, use 'me' for the authenticated user or an email address to which the authenticated user has delegated access.", inputSchema={'type': 'object', 'properties': {'message_id': {'type': 'string', 'description': 'Identifier of the email message to delete.'}, 'user_id': {'type': 'string', 'description': "User's email address. The special value 'me' refers to the authenticated user.", 'default': 'me'}}, 'required': ['message_id'], 'additionalProperties': False, '$schema': 'http://json-schema.org/draft-07/schema#'}, annotations=ToolAnnotations(title='GMAIL_DELETE_MESSAGE', readOnlyHint=False, destructiveHint=True, idempotentHint=False, openWorldHint=True, scopes=[])), Tool(name='GMAIL_FETCH_EMAILS', description='Fetches a list of email messages from a gmail account, supporting filtering, pagination, and optional full content retrieval.', inputSchema={'type': 'object', 'properties': {'include_payload': {'type': 'boolean', 'description': 'Set to true to include full message payload (headers, body, attachments); false for metadata only.', 'default': True}, 'include_spam_trash': {'type': 'boolean', 'description': "Set to true to include messages from 'SPAM' and 'TRASH'.", 'default': False}, 'label_ids': {'type': 'array', 'items': {'type': 'string'}, 'description': "Filter by label IDs; only messages with all specified labels are returned. Common IDs: 'INBOX', 'SPAM', 'TRASH', 'UNREAD', 'STARRED', 'IMPORTANT', 'CATEGORY_PERSONAL', 'CATEGORY_SOCIAL', 'CATEGORY_PROMOTIONS', 'CATEGORY_UPDATES', 'CATEGORY_FORUMS'. Use 'listLabels' action for custom IDs."}, 'max_results': {'type': 'integer', 'minimum': 1, 'maximum': 500, 'description': 'Maximum number of messages to retrieve per page.', 'default': 1}, 'page_token': {'type': ['string', 'null'], 'description': "Token for retrieving a specific page, obtained from a previous response's `nextPageToken`. Omit for the first page.", 'default': None}, 'query': {'type': ['string', 'null'], 'description': "Gmail advanced search query (e.g., 'from:user subject:meeting'). Supports operators like 'from:', 'to:', 'subject:', 'label:', 'has:attachment', 'is:unread', 'after:YYYY/MM/DD', 'before:YYYY/MM/DD', AND/OR/NOT. Use quotes for exact phrases. Omit for no query filter.", 'default': None}, 'user_id': {'type': 'string', 'description': "User's email address or 'me' for the authenticated user.", 'default': 'me'}}, 'additionalProperties': False, '$schema': 'http://json-schema.org/draft-07/schema#'}, annotations=ToolAnnotations(title='GMAIL_FETCH_EMAILS', readOnlyHint=True, destructiveHint=False, idempotentHint=False, openWorldHint=True, scopes=[])), Tool(name='GMAIL_GET_CONTACTS', description='Fetches contacts (connections) for the authenticated google account, allowing selection of specific data fields and pagination.', inputSchema={'type': 'object', 'properties': {'page_token': {'type': 'string', 'description': "Token to retrieve a specific page of results, obtained from 'nextPageToken' in a previous response."}, 'person_fields': {'type': 'string', 'description': "Comma-separated person fields to retrieve for each contact (e.g., 'names,emailAddresses').", 'default': 'emailAddresses,names,birthdays,genders'}, 'resource_name': {'type': 'string', 'description': "Identifier for the person resource whose connections are listed; use 'people/me' for the authenticated user.", 'default': 'people/me'}}, 'additionalProperties': False, '$schema': 'http://json-schema.org/draft-07/schema#'}, annotations=ToolAnnotations(title='GMAIL_GET_CONTACTS', readOnlyHint=True, destructiveHint=False, idempotentHint=False, openWorldHint=True, scopes=[])), Tool(name='GMAIL_LIST_DRAFTS', description="Retrieves a paginated list of email drafts from a user's gmail account.", inputSchema={'type': 'object', 'properties': {'max_results': {'type': 'integer', 'minimum': 1, 'maximum': 500, 'description': 'Maximum number of drafts to return per page.', 'default': 1}, 'page_token': {'type': 'string', 'description': 'Token from a previous response to retrieve a specific page of drafts.', 'default': ''}, 'user_id': {'type': 'string', 'description': "User's mailbox ID; use 'me' for the authenticated user.", 'default': 'me'}}, 'additionalProperties': False, '$schema': 'http://json-schema.org/draft-07/schema#'}, annotations=ToolAnnotations(title='GMAIL_LIST_DRAFTS', readOnlyHint=True, destructiveHint=False, idempotentHint=False, openWorldHint=True, scopes=[])), Tool(name='GMAIL_MOVE_TO_TRASH', description='Moves an existing, non-deleted email message to the trash for the specified user.', inputSchema={'type': 'object', 'properties': {'message_id': {'type': 'string', 'description': 'Identifier of the email message to move to trash.'}, 'user_id': {'type': 'string', 'description': "User's email address or 'me' for the authenticated user.", 'default': 'me'}}, 'required': ['message_id'], 'additionalProperties': False, '$schema': 'http://json-schema.org/draft-07/schema#'}, annotations=ToolAnnotations(title='GMAIL_MOVE_TO_TRASH', readOnlyHint=False, destructiveHint=True, idempotentHint=False, openWorldHint=True, scopes=[])), Tool(name='GMAIL_PATCH_LABEL', description='Patches the specified label.', inputSchema={'type': 'object', 'properties': {'color': {'description': 'The color to assign to the label. Color is only available for labels that have their `type` set to `user`.', 'default': None, 'type': 'object', 'properties': {'backgroundColor': {'type': ['string', 'null'], 'description': 'The background color of the label, represented as a hex string. Example: #ffffff', 'default': None}, 'textColor': {'type': ['string', 'null'], 'description': 'The text color of the label, represented as a hex string. Example: #000000', 'default': None}}, 'additionalProperties': False, 'nullable': True}, 'id': {'type': 'string', 'description': 'The ID of the label to update.'}, 'labelListVisibility': {'type': ['string', 'null'], 'description': 'The visibility of the label in the label list in the Gmail web interface.', 'default': None}, 'messageListVisibility': {'type': ['string', 'null'], 'description': 'The visibility of messages with this label in the message list in the Gmail web interface.', 'default': None}, 'name': {'type': ['string', 'null'], 'description': 'The display name of the label.', 'default': None}, 'userId': {'type': 'string', 'description': "The user's email address. The special value `me` can be used to indicate the authenticated user."}}, 'required': ['id', 'userId'], 'additionalProperties': False, '$schema': 'http://json-schema.org/draft-07/schema#'}, annotations=ToolAnnotations(title='GMAIL_PATCH_LABEL', readOnlyHint=False, destructiveHint=False, idempotentHint=True, openWorldHint=True, scopes=[])), Tool(name='GMAIL_REPLY_TO_THREAD', description="Sends a reply within a specific gmail thread using the original thread's subject, requiring a valid `thread id` and correctly formatted email addresses.", inputSchema={'type': 'object', 'properties': {'bcc': {'type': 'array', 'items': {'type': 'string'}, 'description': "BCC recipients' email addresses (hidden from other recipients).", 'default': []}, 'cc': {'type': 'array', 'items': {'type': 'string'}, 'description': "CC recipients' email addresses.", 'default': []}, 'extra_recipients': {'type': 'array', 'items': {'type': 'string'}, 'description': "Additional 'To' recipients' email addresses.", 'default': []}, 'is_html': {'type': 'boolean', 'description': 'Indicates if `message_body` is HTML; if True, body must be valid HTML, if False, body should not contain HTML tags.', 'default': False}, 'message_body': {'type': 'string', 'description': 'Content of the reply message, either plain text or HTML.'}, 'recipient_email': {'type': 'string', 'description': "Primary recipient's email address."}, 'thread_id': {'type': 'string', 'description': 'Identifier of the Gmail thread for the reply.'}, 'user_id': {'type': 'string', 'description': "Identifier for the user sending the reply; 'me' refers to the authenticated user.", 'default': 'me'}}, 'required': ['message_body', 'recipient_email', 'thread_id'], 'additionalProperties': False, '$schema': 'http://json-schema.org/draft-07/schema#'}, annotations=ToolAnnotations(title='GMAIL_REPLY_TO_THREAD', readOnlyHint=False, destructiveHint=False, idempotentHint=False, openWorldHint=True, scopes=[])), Tool(name='GMAIL_SEARCH_PEOPLE', description="Searches contacts by matching the query against names, nicknames, emails, phone numbers, and organizations, optionally including 'other contacts'.", inputSchema={'type': 'object', 'properties': {'other_contacts': {'type': 'boolean', 'description': "Include 'Other Contacts' (interacted with but not explicitly saved) in search results; if false, searches only primary contacts.", 'default': False}, 'pageSize': {'type': 'integer', 'minimum': 0, 'maximum': 30, 'description': 'Maximum results to return; values >30 are capped to 30 by the API.', 'default': 10}, 'person_fields': {'type': 'string', 'description': "Comma-separated fields to return (e.g., 'names,emailAddresses'); see PersonFields enum. If 'other_contacts' is true, only 'emailAddresses', 'names', 'phoneNumbers' are allowed.", 'default': 'emailAddresses,names,phoneNumbers'}, 'query': {'type': 'string', 'description': 'Matches contact names, nicknames, email addresses, phone numbers, and organization fields.'}}, 'required': ['query'], 'additionalProperties': False, '$schema': 'http://json-schema.org/draft-07/schema#'}, annotations=ToolAnnotations(title='GMAIL_SEARCH_PEOPLE', readOnlyHint=True, destructiveHint=False, idempotentHint=False, openWorldHint=True, scopes=[])), Tool(name='GMAIL_SEND_DRAFT', description='Sends the specified, existing draft to the recipients in the to, cc, and bcc headers.', inputSchema={'type': 'object', 'properties': {'draft_id': {'type': 'string', 'description': 'The ID of the draft to send.'}, 'user_id': {'type': 'string', 'description': "The user's email address. The special value `me` can be used to indicate the authenticated user.", 'default': 'me'}}, 'required': ['draft_id'], 'additionalProperties': False, '$schema': 'http://json-schema.org/draft-07/schema#'}, annotations=ToolAnnotations(title='GMAIL_SEND_DRAFT', readOnlyHint=False, destructiveHint=False, idempotentHint=False, openWorldHint=True, scopes=[])), Tool(name='GMAIL_CHECK_ACTIVE_CONNECTION', description='Check if any active connections exist for the gmail app or verify the status of a connection with a specific ID. If an active connection exists, returns true, otherwise returns false. Active connections allow an agent to perform actions with the gmail app.', inputSchema={'type': 'object', 'properties': {'connection_id': {'type': 'string', 'description': 'ID of the connection to check', 'default': None}, 'tool': {'type': 'string', 'description': 'Name of the tool, for example: GitHub', 'default': None}}, 'required': ['connection_id', 'tool'], 'additionalProperties': False, '$schema': 'http://json-schema.org/draft-07/schema#'}, annotations=ToolAnnotations(title='GMAIL_CHECK_ACTIVE_CONNECTION', readOnlyHint=None, destructiveHint=None, idempotentHint=None, openWorldHint=None, scopes=[])), Tool(name='GMAIL_INITIATE_CONNECTION', description="Initiate a connection to the gmail app. For example, gmail might require specific parameters like 'api_key' to create a connection, which we can get by calling GMAIL_GET_REQUIRED_PARAMETERS. To create a new connection to gmail, call this tool with the required parameters.", inputSchema={'type': 'object', 'properties': {'parameters': {'description': "Optional parameters required for connection. Not needed if using a default connector. Examples: {'api_key': '1234567890'}", 'default': None, 'type': 'object', 'additionalProperties': {}, 'nullable': True}, 'tool': {'type': 'string', 'description': 'Name of the tool like GitHub'}}, 'required': ['tool'], 'additionalProperties': False, '$schema': 'http://json-schema.org/draft-07/schema#'}, annotations=ToolAnnotations(title='GMAIL_INITIATE_CONNECTION', readOnlyHint=None, destructiveHint=None, idempotentHint=None, openWorldHint=None, scopes=[])), Tool(name='GMAIL_GET_REQUIRED_PARAMETERS', description="Retrieves the necessary parameters required to initiate a connection with gmail. Different tools require different parameters based on their authentication scheme. For example, gmail might require an API key, a subdomain, or a webhook URL. Tools using OAuth2 typically don't require parameters as they redirect users to the tool's website for authorization.", inputSchema={'type': 'object', 'properties': {'tool': {'type': 'string', 'description': 'Name of the tool For example: github'}}, 'required': ['tool'], 'additionalProperties': False, '$schema': 'http://json-schema.org/draft-07/schema#'}, annotations=ToolAnnotations(title='GMAIL_GET_REQUIRED_PARAMETERS', readOnlyHint=None, destructiveHint=None, idempotentHint=None, openWorldHint=None, scopes=[]))]}
backend-1 | 🔍 END MCP RESPONSE DEBUG
backend-1 | Connected via HTTP (13 tools across 1 pages)
