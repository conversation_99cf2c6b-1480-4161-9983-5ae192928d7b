import os
import sys
import json
import asyncio
import subprocess
from typing import Dict, Any
from concurrent.futures import Thr<PERSON>PoolExecutor
from fastapi import HTTPException  # type: ignore
from utils.logger import logger
from mcp import ClientSession
from mcp.client.sse import sse_client  # type: ignore
from mcp.client.streamable_http import streamablehttp_client  # type: ignore


async def connect_streamable_http_server(url):
    async with streamablehttp_client(url) as (
        read_stream,
        write_stream,
        _,
    ):
        async with ClientSession(read_stream, write_stream) as session:
            await session.initialize()
            tool_result = await session.list_tools()

            # Enhanced logging for debugging tool discovery
            logger.info(f"🔍 [MCP-DEBUG] Connected via HTTP to {url}")
            logger.info(f"🔍 [MCP-DEBUG] Raw tool_result type: {type(tool_result)}")
            logger.info(
                f"🔍 [MCP-DEBUG] Raw tool_result attributes: {dir(tool_result)}"
            )
            logger.info(f"🔍 [MCP-DEBUG] Tool count: {len(tool_result.tools)}")

            # Check for pagination attributes
            pagination_attrs = [
                "nextCursor",
                "cursor",
                "hasMore",
                "pagination",
                "next",
                "continuation",
            ]
            for attr in pagination_attrs:
                if hasattr(tool_result, attr):
                    value = getattr(tool_result, attr)
                    logger.info(
                        f"🔍 [MCP-DEBUG] Found pagination attribute '{attr}': {value}"
                    )

            # Alert if exactly 13 tools found (potential pagination limit)
            if len(tool_result.tools) == 13:
                logger.warning(
                    f"⚠️ [DEBUG] Exactly 13 tools found - potential pagination limit detected!"
                )
                logger.warning(f"⚠️ [DEBUG] URL: {url}")

            # Log tool names for analysis
            tool_names = [tool.name for tool in tool_result.tools]
            logger.info(f"🔍 [MCP-DEBUG] Tool names: {tool_names}")

            print(f"Connected via HTTP ({len(tool_result.tools)} tools)")

            tools_info = []
            for tool in tool_result.tools:
                tool_info = {
                    "name": tool.name,
                    "description": tool.description,
                    "inputSchema": tool.inputSchema,
                }
                tools_info.append(tool_info)

            return tools_info


async def discover_custom_tools(request_type: str, config: Dict[str, Any]):
    logger.info(
        f"🔍 [DEBUG] Received custom MCP discovery request: type={request_type}"
    )
    logger.debug(f"🔍 [DEBUG] Request config: {config}")

    tools = []
    server_name = None

    if request_type == "http":
        if "url" not in config:
            raise HTTPException(
                status_code=400, detail="HTTP configuration must include 'url' field"
            )
        url = config["url"]

        logger.info(
            f"🔍 [DEBUG] Attempting to discover tools from HTTP MCP server: {url}"
        )

        try:
            async with asyncio.timeout(15):
                tools_info = await connect_streamable_http_server(url)
                logger.info(
                    f"🔍 [DEBUG] Retrieved {len(tools_info)} tools from HTTP server"
                )

                for tool_info in tools_info:
                    tools.append(
                        {
                            "name": tool_info["name"],
                            "description": tool_info["description"],
                            "inputSchema": tool_info["inputSchema"],
                        }
                    )

                # Alert if exactly 13 tools found
                if len(tools) == 13:
                    logger.warning(
                        f"⚠️ [DEBUG] Exactly 13 tools discovered - potential pagination limit!"
                    )

        except asyncio.TimeoutError:
            logger.error(f"🔍 [DEBUG] Connection timeout for URL: {url}")
            raise HTTPException(
                status_code=408,
                detail="Connection timeout - server took too long to respond",
            )
        except Exception as e:
            logger.error(f"🔍 [DEBUG] Error connecting to HTTP MCP server {url}: {e}")
            raise HTTPException(
                status_code=400, detail=f"Failed to connect to MCP server: {str(e)}"
            )

    elif request_type == "sse":
        if "url" not in config:
            raise HTTPException(
                status_code=400, detail="SSE configuration must include 'url' field"
            )

        url = config["url"]
        headers = config.get("headers", {})

        try:
            async with asyncio.timeout(15):
                try:
                    async with sse_client(url, headers=headers) as (read, write):
                        async with ClientSession(read, write) as session:
                            await session.initialize()
                            tools_result = await session.list_tools()
                            tools_info = []
                            for tool in tools_result.tools:
                                tool_info = {
                                    "name": tool.name,
                                    "description": tool.description,
                                    "input_schema": tool.inputSchema,
                                }
                                tools_info.append(tool_info)

                            for tool_info in tools_info:
                                tools.append(
                                    {
                                        "name": tool_info["name"],
                                        "description": tool_info["description"],
                                        "inputSchema": tool_info["input_schema"],
                                    }
                                )
                except TypeError as e:
                    if "unexpected keyword argument" in str(e):
                        async with sse_client(url) as (read, write):
                            async with ClientSession(read, write) as session:
                                await session.initialize()
                                tools_result = await session.list_tools()
                                tools_info = []
                                for tool in tools_result.tools:
                                    tool_info = {
                                        "name": tool.name,
                                        "description": tool.description,
                                        "input_schema": tool.inputSchema,
                                    }
                                    tools_info.append(tool_info)

                                for tool_info in tools_info:
                                    tools.append(
                                        {
                                            "name": tool_info["name"],
                                            "description": tool_info["description"],
                                            "inputSchema": tool_info["input_schema"],
                                        }
                                    )
                    else:
                        raise
        except asyncio.TimeoutError:
            raise HTTPException(
                status_code=408,
                detail="Connection timeout - server took too long to respond",
            )
        except Exception as e:
            logger.error(f"Error connecting to SSE MCP server: {e}")
            raise HTTPException(
                status_code=400, detail=f"Failed to connect to MCP server: {str(e)}"
            )
    else:
        raise HTTPException(
            status_code=400, detail="Invalid server type. Must be 'http' or 'sse'"
        )

    response_data = {"tools": tools, "count": len(tools)}

    if server_name:
        response_data["serverName"] = server_name

    logger.info(f"Returning {len(tools)} tools for server {server_name}")
    return response_data
