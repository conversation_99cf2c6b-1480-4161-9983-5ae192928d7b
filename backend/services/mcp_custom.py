import os
import sys
import json
import asyncio
import subprocess
from typing import Dict, Any
from concurrent.futures import Thread<PERSON>oolExecutor
from fastapi import H<PERSON>P<PERSON>x<PERSON>  # type: ignore
from utils.logger import logger
from mcp import ClientSession
from mcp.client.sse import sse_client  # type: ignore
from mcp.client.streamable_http import streamablehttp_client  # type: ignore


async def connect_streamable_http_server(url):
    print(f"🔍 CONNECTING TO MCP SERVER: {url}")
    print(f"🔍 URL HASH: {hash(url)}")  # To track if URL changes
    async with streamablehttp_client(url) as (
        read_stream,
        write_stream,
        _,
    ):
        async with ClientSession(read_stream, write_stream) as session:
            await session.initialize()
            print(f"🔍 MCP SESSION INITIALIZED FOR: {url}")

            # Collect all tools across all pages
            all_tools = []
            cursor = None
            page_count = 0
            max_pages = 10  # Safety limit to prevent infinite loops

            while page_count < max_pages:
                page_count += 1

                # Call list_tools with cursor for pagination
                try:
                    if cursor:
                        # Try to pass cursor parameter (may not be supported in current SDK)
                        tool_result = await session.list_tools(cursor=cursor)
                    else:
                        tool_result = await session.list_tools()
                except TypeError:
                    # Fallback: cursor parameter not supported in current SDK
                    tool_result = await session.list_tools()

                # Add tools from this page
                page_tools = tool_result.tools if hasattr(tool_result, "tools") else []
                all_tools.extend(page_tools)

                # Check for next page
                next_cursor = None
                if hasattr(tool_result, "nextCursor") and tool_result.nextCursor:
                    next_cursor = tool_result.nextCursor
                elif hasattr(tool_result, "cursor") and tool_result.cursor:
                    next_cursor = tool_result.cursor

                # Log detailed response info for debugging (using print to ensure visibility)
                if page_count == 1:
                    print(f"🔍 MCP RESPONSE DEBUG:")
                    print(f"  Type: {type(tool_result)}")
                    print(f"  Attributes: {dir(tool_result)}")

                    # Log all attributes and their values
                    print(f"  Attribute values:")
                    for attr in dir(tool_result):
                        if not attr.startswith("_"):
                            try:
                                value = getattr(tool_result, attr)
                                if not callable(value):
                                    print(f"    {attr}: {value}")
                            except Exception as e:
                                print(f"    {attr}: <error getting value: {e}>")

                    # Try to convert to dict if possible
                    try:
                        if hasattr(tool_result, "__dict__"):
                            print(f"  Response __dict__: {tool_result.__dict__}")
                        elif hasattr(tool_result, "model_dump"):
                            print(f"  Response model_dump: {tool_result.model_dump()}")
                    except Exception as e:
                        print(f"  Could not serialize response: {e}")
                    print(f"🔍 END MCP RESPONSE DEBUG")

                if next_cursor:
                    cursor = next_cursor
                    logger.info(f"Continuing to next page with cursor: {next_cursor}")
                else:
                    logger.info("No nextCursor found, pagination complete")
                    break

            print(
                f"Connected via HTTP ({len(all_tools)} tools across {page_count} pages)"
            )

            tools_info = []
            for tool in all_tools:
                tool_info = {
                    "name": tool.name,
                    "description": tool.description,
                    "inputSchema": tool.inputSchema,
                }
                tools_info.append(tool_info)

            return tools_info


async def discover_custom_tools(request_type: str, config: Dict[str, Any]):
    logger.info(f"Received custom MCP discovery request: type={request_type}")
    logger.debug(f"Request config: {config}")

    tools = []
    server_name = None

    if request_type == "http":
        if "url" not in config:
            raise HTTPException(
                status_code=400, detail="HTTP configuration must include 'url' field"
            )
        url = config["url"]

        try:
            async with asyncio.timeout(15):
                tools_info = await connect_streamable_http_server(url)

                for tool_info in tools_info:
                    tools.append(
                        {
                            "name": tool_info["name"],
                            "description": tool_info["description"],
                            "inputSchema": tool_info["inputSchema"],
                        }
                    )

        except asyncio.TimeoutError:
            raise HTTPException(
                status_code=408,
                detail="Connection timeout - server took too long to respond",
            )
        except Exception as e:
            logger.error(f"Error connecting to HTTP MCP server: {e}")
            raise HTTPException(
                status_code=400, detail=f"Failed to connect to MCP server: {str(e)}"
            )

    elif request_type == "sse":
        if "url" not in config:
            raise HTTPException(
                status_code=400, detail="SSE configuration must include 'url' field"
            )

        url = config["url"]
        headers = config.get("headers", {})

        try:
            async with asyncio.timeout(15):
                try:
                    async with sse_client(url, headers=headers) as (read, write):
                        async with ClientSession(read, write) as session:
                            await session.initialize()
                            tools_result = await session.list_tools()
                            tools_info = []
                            for tool in tools_result.tools:
                                tool_info = {
                                    "name": tool.name,
                                    "description": tool.description,
                                    "input_schema": tool.inputSchema,
                                }
                                tools_info.append(tool_info)

                            for tool_info in tools_info:
                                tools.append(
                                    {
                                        "name": tool_info["name"],
                                        "description": tool_info["description"],
                                        "inputSchema": tool_info["input_schema"],
                                    }
                                )
                except TypeError as e:
                    if "unexpected keyword argument" in str(e):
                        async with sse_client(url) as (read, write):
                            async with ClientSession(read, write) as session:
                                await session.initialize()
                                tools_result = await session.list_tools()
                                tools_info = []
                                for tool in tools_result.tools:
                                    tool_info = {
                                        "name": tool.name,
                                        "description": tool.description,
                                        "input_schema": tool.inputSchema,
                                    }
                                    tools_info.append(tool_info)

                                for tool_info in tools_info:
                                    tools.append(
                                        {
                                            "name": tool_info["name"],
                                            "description": tool_info["description"],
                                            "inputSchema": tool_info["input_schema"],
                                        }
                                    )
                    else:
                        raise
        except asyncio.TimeoutError:
            raise HTTPException(
                status_code=408,
                detail="Connection timeout - server took too long to respond",
            )
        except Exception as e:
            logger.error(f"Error connecting to SSE MCP server: {e}")
            raise HTTPException(
                status_code=400, detail=f"Failed to connect to MCP server: {str(e)}"
            )
    else:
        raise HTTPException(
            status_code=400, detail="Invalid server type. Must be 'http' or 'sse'"
        )

    response_data = {"tools": tools, "count": len(tools)}

    if server_name:
        response_data["serverName"] = server_name

    logger.info(f"Returning {len(tools)} tools for server {server_name}")
    return response_data
