import os
import sys
import json
import asyncio
import subprocess
from typing import Dict, Any
from concurrent.futures import ThreadPoolExecutor
from fastapi import HTTPException  # type: ignore
from utils.logger import logger
from mcp import ClientSession
from mcp.client.sse import sse_client  # type: ignore
from mcp.client.streamable_http import streamablehttp_client  # type: ignore


async def connect_streamable_http_server(url):
    async with streamablehttp_client(url) as (
        read_stream,
        write_stream,
        _,
    ):
        async with ClientSession(read_stream, write_stream) as session:
            await session.initialize()

            # Collect all tools across all pages
            all_tools = []
            cursor = None
            page_count = 0
            max_pages = 10  # Safety limit to prevent infinite loops

            while page_count < max_pages:
                page_count += 1

                # Call list_tools with cursor for pagination
                if cursor:
                    tool_result = await session.list_tools(cursor=cursor)
                    logger.info(f"Fetching page {page_count} with cursor: {cursor}")
                else:
                    tool_result = await session.list_tools()
                    logger.info(f"Fetching page {page_count} (initial page)")

                # Add tools from this page
                page_tools = tool_result.tools if hasattr(tool_result, "tools") else []
                all_tools.extend(page_tools)

                logger.info(f"Page {page_count}: Found {len(page_tools)} tools")

                # Check for next page
                next_cursor = None
                if hasattr(tool_result, "nextCursor") and tool_result.nextCursor:
                    next_cursor = tool_result.nextCursor
                elif hasattr(tool_result, "cursor") and tool_result.cursor:
                    next_cursor = tool_result.cursor

                if next_cursor:
                    cursor = next_cursor
                    logger.info(
                        f"Found nextCursor: {next_cursor}, continuing to next page"
                    )
                else:
                    logger.info("No more pages available")
                    break

            logger.info(
                f"Total tools collected across {page_count} pages: {len(all_tools)}"
            )
            print(
                f"Connected via HTTP ({len(all_tools)} tools across {page_count} pages)"
            )

            tools_info = []
            for tool in all_tools:
                tool_info = {
                    "name": tool.name,
                    "description": tool.description,
                    "inputSchema": tool.inputSchema,
                }
                tools_info.append(tool_info)

            return tools_info


async def discover_custom_tools(request_type: str, config: Dict[str, Any]):
    logger.info(
        f"🔍 [DEBUG] Received custom MCP discovery request: type={request_type}"
    )
    logger.debug(f"🔍 [DEBUG] Request config: {config}")

    tools = []
    server_name = None

    if request_type == "http":
        if "url" not in config:
            raise HTTPException(
                status_code=400, detail="HTTP configuration must include 'url' field"
            )
        url = config["url"]

        logger.info(
            f"🔍 [DEBUG] Attempting to discover tools from HTTP MCP server: {url}"
        )

        try:
            async with asyncio.timeout(15):
                tools_info = await connect_streamable_http_server(url)
                logger.info(
                    f"🔍 [DEBUG] Retrieved {len(tools_info)} tools from HTTP server"
                )

                for tool_info in tools_info:
                    tools.append(
                        {
                            "name": tool_info["name"],
                            "description": tool_info["description"],
                            "inputSchema": tool_info["inputSchema"],
                        }
                    )

                # Alert if exactly 13 tools found
                if len(tools) == 13:
                    logger.warning(
                        f"⚠️ [DEBUG] Exactly 13 tools discovered - potential pagination limit!"
                    )

        except asyncio.TimeoutError:
            logger.error(f"🔍 [DEBUG] Connection timeout for URL: {url}")
            raise HTTPException(
                status_code=408,
                detail="Connection timeout - server took too long to respond",
            )
        except Exception as e:
            logger.error(f"🔍 [DEBUG] Error connecting to HTTP MCP server {url}: {e}")
            raise HTTPException(
                status_code=400, detail=f"Failed to connect to MCP server: {str(e)}"
            )

    elif request_type == "sse":
        if "url" not in config:
            raise HTTPException(
                status_code=400, detail="SSE configuration must include 'url' field"
            )

        url = config["url"]
        headers = config.get("headers", {})

        try:
            async with asyncio.timeout(15):
                try:
                    async with sse_client(url, headers=headers) as (read, write):
                        async with ClientSession(read, write) as session:
                            await session.initialize()
                            tools_result = await session.list_tools()
                            tools_info = []
                            for tool in tools_result.tools:
                                tool_info = {
                                    "name": tool.name,
                                    "description": tool.description,
                                    "input_schema": tool.inputSchema,
                                }
                                tools_info.append(tool_info)

                            for tool_info in tools_info:
                                tools.append(
                                    {
                                        "name": tool_info["name"],
                                        "description": tool_info["description"],
                                        "inputSchema": tool_info["input_schema"],
                                    }
                                )
                except TypeError as e:
                    if "unexpected keyword argument" in str(e):
                        async with sse_client(url) as (read, write):
                            async with ClientSession(read, write) as session:
                                await session.initialize()
                                tools_result = await session.list_tools()
                                tools_info = []
                                for tool in tools_result.tools:
                                    tool_info = {
                                        "name": tool.name,
                                        "description": tool.description,
                                        "input_schema": tool.inputSchema,
                                    }
                                    tools_info.append(tool_info)

                                for tool_info in tools_info:
                                    tools.append(
                                        {
                                            "name": tool_info["name"],
                                            "description": tool_info["description"],
                                            "inputSchema": tool_info["input_schema"],
                                        }
                                    )
                    else:
                        raise
        except asyncio.TimeoutError:
            raise HTTPException(
                status_code=408,
                detail="Connection timeout - server took too long to respond",
            )
        except Exception as e:
            logger.error(f"Error connecting to SSE MCP server: {e}")
            raise HTTPException(
                status_code=400, detail=f"Failed to connect to MCP server: {str(e)}"
            )
    else:
        raise HTTPException(
            status_code=400, detail="Invalid server type. Must be 'http' or 'sse'"
        )

    response_data = {"tools": tools, "count": len(tools)}

    if server_name:
        response_data["serverName"] = server_name

    logger.info(f"Returning {len(tools)} tools for server {server_name}")
    return response_data
