#!/usr/bin/env python3
"""
Test script to get tools from a specific Composio MCP URL.
"""

import asyncio
import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from mcp.client.streamable_http import streamablehttp_client
from mcp import ClientSession

async def get_tools_from_url(url):
    """Get tools from a specific MCP URL."""
    print(f"🔍 CONNECTING TO: {url}")
    
    try:
        async with streamablehttp_client(url) as (read_stream, write_stream, _):
            async with ClientSession(read_stream, write_stream) as session:
                print("🔍 Initializing session...")
                await session.initialize()
                print("✅ Session initialized")
                
                print("🔍 Listing tools...")
                tool_result = await session.list_tools()
                
                tools = tool_result.tools if hasattr(tool_result, 'tools') else []
                print(f"✅ Found {len(tools)} tools")
                
                print(f"\n📋 ALL {len(tools)} TOOLS:")
                for i, tool in enumerate(tools, 1):
                    print(f"  {i:2d}. {tool.name}")
                    print(f"      Description: {tool.description[:100]}...")
                    if hasattr(tool, 'inputSchema') and tool.inputSchema:
                        schema = tool.inputSchema
                        if isinstance(schema, dict) and 'properties' in schema:
                            props = list(schema['properties'].keys())[:3]  # First 3 properties
                            print(f"      Parameters: {props}...")
                    print()
                
                # Check for GMAIL_SEND_EMAIL specifically
                tool_names = [tool.name for tool in tools]
                if 'GMAIL_SEND_EMAIL' in tool_names:
                    print("✅ GMAIL_SEND_EMAIL found!")
                    # Get details of GMAIL_SEND_EMAIL
                    for tool in tools:
                        if tool.name == 'GMAIL_SEND_EMAIL':
                            print(f"📧 GMAIL_SEND_EMAIL details:")
                            print(f"   Description: {tool.description}")
                            if hasattr(tool, 'inputSchema'):
                                print(f"   Schema: {tool.inputSchema}")
                else:
                    print("❌ GMAIL_SEND_EMAIL NOT found")
                
                # Check for send-related tools
                send_tools = [name for name in tool_names if 'SEND' in name.upper()]
                if send_tools:
                    print(f"📤 Send-related tools: {send_tools}")
                
                return tools
                
    except Exception as e:
        print(f"❌ ERROR: {e}")
        import traceback
        traceback.print_exc()
        return []

async def main():
    """Test the specific URL."""
    url = "https://mcp.composio.dev/partner/composio/gmail/mcp?customerId=f93eda9b-d40f-403c-a947-ad1d985408b5"
    
    tools = await get_tools_from_url(url)
    
    print(f"\n🎯 SUMMARY:")
    print(f"   Total tools: {len(tools)}")
    if tools:
        tool_names = [tool.name for tool in tools]
        has_send_email = 'GMAIL_SEND_EMAIL' in tool_names
        has_send_draft = 'GMAIL_SEND_DRAFT' in tool_names
        print(f"   GMAIL_SEND_EMAIL: {'✅' if has_send_email else '❌'}")
        print(f"   GMAIL_SEND_DRAFT: {'✅' if has_send_draft else '❌'}")

if __name__ == "__main__":
    asyncio.run(main())
