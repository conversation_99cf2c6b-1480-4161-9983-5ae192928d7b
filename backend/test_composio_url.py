#!/usr/bin/env python3
"""
Test script to check Composio MCP URL formats and compare tool responses.
"""

import asyncio
import sys
import os

sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from mcp.client.streamable_http import streamablehttp_client
from mcp import ClientSession


async def test_composio_url(url, description):
    """Test a specific Composio MCP URL and return tools."""
    print(f"\n🔍 TESTING {description}")
    print(f"URL: {url}")

    try:
        async with streamablehttp_client(url) as (read_stream, write_stream, _):
            async with ClientSession(read_stream, write_stream) as session:
                await session.initialize()
                tool_result = await session.list_tools()

                tools = tool_result.tools if hasattr(tool_result, "tools") else []
                print(f"✅ SUCCESS: Found {len(tools)} tools")

                # Show ALL tool names
                all_tool_names = [tool.name for tool in tools]
                print(f"ALL {len(tools)} tools:")
                for i, name in enumerate(all_tool_names, 1):
                    print(f"  {i:2d}. {name}")

                # Check for GMAIL_SEND_EMAIL specifically
                if "GMAIL_SEND_EMAIL" in all_tool_names:
                    print("✅ GMAIL_SEND_EMAIL found!")
                else:
                    print("❌ GMAIL_SEND_EMAIL NOT found")

                # Check for other email sending tools
                send_tools = [name for name in all_tool_names if "SEND" in name.upper()]
                if send_tools:
                    print(f"Send-related tools: {send_tools}")

                # Check for tools mentioned on website
                website_tools = [
                    "GMAIL_DELETE_DRAFT",
                    "GMAIL_DELETE_MESSAGE",
                    "GMAIL_FETCH_EMAILS",
                    "GMAIL_GET_CONTACTS",
                    "GMAIL_LIST_DRAFTS",
                    "GMAIL_MOVE_TO_TRASH",
                    "GMAIL_PATCH_LABEL",
                    "GMAIL_REPLY_TO_THREAD",
                    "GMAIL_SEARCH_PEOPLE",
                    "GMAIL_SEND_DRAFT",
                    "GMAIL_SEND_EMAIL",
                ]

                missing_from_website = [
                    tool for tool in all_tool_names if tool not in website_tools
                ]
                missing_from_mcp = [
                    tool for tool in website_tools if tool not in all_tool_names
                ]

                if missing_from_website:
                    print(f"🔍 Extra tools not on website: {missing_from_website}")
                if missing_from_mcp:
                    print(f"🔍 Website tools missing from MCP: {missing_from_mcp}")

                return tools

    except Exception as e:
        print(f"❌ ERROR: {e}")
        return []


async def main():
    """Test different Composio URL formats."""

    # Test the URL format you mentioned
    customer_id = "f93eda9b-d40f-403c-a947-ad1d985408b5"

    # Test different URL formats
    urls_to_test = [
        (
            f"https://mcp.composio.dev/partner/composio/gmail/mcp?customerId={customer_id}",
            "MCP format (/mcp)",
        ),
        (
            f"https://mcp.composio.dev/partner/composio/gmail/http?customerId={customer_id}",
            "HTTP format (/http)",
        ),
        (
            f"https://mcp.composio.dev/partner/composio/gmail/sse?customerId={customer_id}",
            "SSE format (/sse)",
        ),
    ]

    results = {}

    for url, description in urls_to_test:
        tools = await test_composio_url(url, description)
        results[description] = tools

    # Compare results
    print(f"\n🔍 COMPARISON SUMMARY:")
    for description, tools in results.items():
        tool_names = [tool.name for tool in tools] if tools else []
        has_send_email = "GMAIL_SEND_EMAIL" in tool_names
        print(
            f"{description}: {len(tools)} tools, GMAIL_SEND_EMAIL: {'✅' if has_send_email else '❌'}"
        )


if __name__ == "__main__":
    asyncio.run(main())
